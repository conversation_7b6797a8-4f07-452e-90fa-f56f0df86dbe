# src/algorithms/evaluators/finger_touch.py
import numpy as np
from typing import Tuple
from .base_evaluator import BaseActionEvaluator
from models.constants import KeyPointMapping as K
from utils.pose_math import pose_math
from utils.coordinate_utils import CoordinateUtils

class FingerTouchEvaluator(BaseActionEvaluator):
    """对指动作评估器 - 专注于实时评分和完成判断"""
    
    def __init__(self, action_info):
        super().__init__(action_info)
        # 动作特定的阈值 - 适配RTMPose坐标系
        self.touch_distance_threshold = 0.05  # 双手指尖距离阈值
        self.optimal_distance = 0.03  # 最佳接触距离
        self.hand_height_threshold = 0.1  # 手部抬举的最低高度
        
    def calculate_score(self) -> float:
        """计算对指动作实时得分"""
        kps = self.kps
        
        if not kps:
            return 0.0
        
        # 获取关键点
        left_wrist = pose_math.get_keypoints(kps, [K.LEFT_WRIST])[0]
        right_wrist = pose_math.get_keypoints(kps, [K.RIGHT_WRIST])[0]
        left_elbow = pose_math.get_keypoints(kps, [K.LEFT_ELBOW])[0]
        right_elbow = pose_math.get_keypoints(kps, [K.RIGHT_ELBOW])[0]
        left_shoulder = pose_math.get_keypoints(kps, [K.LEFT_SHOULDER])[0]
        right_shoulder = pose_math.get_keypoints(kps, [K.RIGHT_SHOULDER])[0]
        nose = pose_math.get_keypoints(kps, [K.NOSE])[0]
        
        required_points = [left_wrist, right_wrist, left_elbow, right_elbow, 
                          left_shoulder, right_shoulder, nose]
        
        if not all(pose_math.is_valid_keypoint(kp) for kp in required_points):
            return 0.0
        
        # 计算多个评估指标
        distance_score = self._evaluate_finger_distance(left_wrist, right_wrist)
        height_score = self._evaluate_hand_height(left_wrist, right_wrist, nose)
        symmetry_score = self._evaluate_hand_symmetry(left_wrist, right_wrist, 
                                                     left_elbow, right_elbow,
                                                     left_shoulder, right_shoulder)
        posture_score = self._evaluate_arm_posture(left_shoulder, left_elbow, left_wrist,
                                                  right_shoulder, right_elbow, right_wrist)
        
        # 加权综合得分
        total_score = (
            distance_score * 0.4 +     # 指尖距离最重要
            height_score * 0.3 +       # 抬举高度
            symmetry_score * 0.2 +     # 动作对称性
            posture_score * 0.1        # 手臂姿态
        )
        
        return min(100.0, max(0.0, total_score))
    
    def _evaluate_finger_distance(self, left_wrist: np.ndarray, right_wrist: np.ndarray) -> float:
        """评估双手指尖距离"""
        distance = CoordinateUtils.calculate_normalized_distance(left_wrist, right_wrist)
        
        if distance <= self.optimal_distance:
            return 100.0  # 完美接触
        elif distance <= self.touch_distance_threshold:
            # 线性衰减
            progress = (self.touch_distance_threshold - distance) / (self.touch_distance_threshold - self.optimal_distance)
            return 70.0 + progress * 30.0
        elif distance <= self.touch_distance_threshold * 2:
            return 40.0  # 距离稍远但可接受
        else:
            return 10.0  # 距离太远
    
    def _evaluate_hand_height(self, left_wrist: np.ndarray, right_wrist: np.ndarray, 
                             nose: np.ndarray) -> float:
        """评估手部抬举高度"""
        # 计算双手相对于鼻子的高度
        left_height = nose[1] - left_wrist[1]  # Y轴值越小表示越高
        right_height = nose[1] - right_wrist[1]
        
        # 取两手中较低的那个作为评估标准
        min_height = min(left_height, right_height)
        
        if min_height >= self.hand_height_threshold:
            return 100.0  # 抬举高度足够
        elif min_height >= self.hand_height_threshold * 0.7:
            return 80.0   # 抬举高度较好
        elif min_height >= self.hand_height_threshold * 0.5:
            return 60.0   # 抬举高度一般
        elif min_height >= 0:
            return 40.0   # 抬举高度不足但有抬举
        else:
            return 0.0    # 手部低于鼻子
    
    def _evaluate_hand_symmetry(self, left_wrist: np.ndarray, right_wrist: np.ndarray,
                               left_elbow: np.ndarray, right_elbow: np.ndarray,
                               left_shoulder: np.ndarray, right_shoulder: np.ndarray) -> float:
        """评估双手动作对称性"""
        # 计算身体中线
        body_center_x = (left_shoulder[0] + right_shoulder[0]) / 2
        
        # 计算双手相对于中线的距离
        left_offset = abs(left_wrist[0] - body_center_x)
        right_offset = abs(right_wrist[0] - body_center_x)
        
        # 对称性评估：两手到中线的距离应该相近
        symmetry_diff = abs(left_offset - right_offset)
        
        if symmetry_diff <= 20:
            return 100.0  # 非常对称
        elif symmetry_diff <= 40:
            return 80.0   # 较对称
        elif symmetry_diff <= 60:
            return 60.0   # 一般对称
        else:
            return 40.0   # 不够对称
    
    def _evaluate_arm_posture(self, left_shoulder: np.ndarray, left_elbow: np.ndarray, left_wrist: np.ndarray,
                             right_shoulder: np.ndarray, right_elbow: np.ndarray, right_wrist: np.ndarray) -> float:
        """评估双臂姿态"""
        # 计算双臂的弯曲角度
        left_arm_angle = pose_math.calculate_angle(left_shoulder, left_elbow, left_wrist)
        right_arm_angle = pose_math.calculate_angle(right_shoulder, right_elbow, right_wrist)
        
        # 理想的手臂角度范围：90-130度（适度弯曲）
        def angle_score(angle):
            if 90 <= angle <= 130:
                return 100.0
            elif 70 <= angle < 90 or 130 < angle <= 150:
                return 80.0
            elif 50 <= angle < 70 or 150 < angle <= 170:
                return 60.0
            else:
                return 40.0
        
        left_score = angle_score(left_arm_angle)
        right_score = angle_score(right_arm_angle)
        
        # 取平均值
        return (left_score + right_score) / 2
