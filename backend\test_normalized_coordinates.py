# test_normalized_coordinates.py
"""
测试归一化坐标处理
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from algorithms.evaluators_factory import EvaluatorFactory
from models.data_models import ActionInfo
from utils.coordinate_utils import CoordinateUtils

def create_test_keypoints():
    """创建测试用的RTMPose关键点数据"""
    # 模拟一个人正在举左手的姿态
    # 基于实际观察到的RTMPose坐标范围
    keypoints = []
    
    # 身体关键点 (0-16) - RTMPose坐标系
    body_points = [
        [1.0, 0.45, 0.9],      # NOSE - 鼻子
        [0.98, 0.43, 0.8],     # LEFT_EYE  
        [1.02, 0.43, 0.8],     # RIGHT_EYE
        [0.96, 0.44, 0.7],     # LEFT_EAR
        [1.04, 0.44, 0.7],     # RIGHT_EAR
        [0.93, 0.55, 0.9],     # LEFT_SHOULDER - 左肩
        [1.07, 0.55, 0.9],     # RIGHT_SHOULDER - 右肩
        [0.85, 0.40, 0.8],     # LEFT_ELBOW - 左肘（举起状态）
        [1.11, 0.70, 0.8],     # RIGHT_ELBOW - 右肘
        [0.82, 0.25, 0.8],     # LEFT_WRIST - 左手腕（举到头部上方）
        [1.16, 0.80, 0.8],     # RIGHT_WRIST - 右手腕
        [0.95, 0.80, 0.7],     # LEFT_HIP
        [1.05, 0.80, 0.7],     # RIGHT_HIP
        [0.94, 1.00, 0.6],     # LEFT_KNEE
        [1.06, 1.00, 0.6],     # RIGHT_KNEE
        [0.94, 1.20, 0.5],     # LEFT_ANKLE
        [1.06, 1.20, 0.5],     # RIGHT_ANKLE
    ]
    keypoints.extend(body_points)
    
    # 添加其他关键点（简化版本）
    # 脚部关键点 (17-22)
    for i in range(6):
        keypoints.append([1.0, 1.2, 0.3])
    
    # 面部关键点 (23-90) - 68个点
    for i in range(68):
        keypoints.append([1.0, 0.45, 0.3])
    
    # 左手关键点 (91-111) - 21个点
    for i in range(21):
        keypoints.append([0.82, 0.25, 0.5])
    
    # 右手关键点 (112-132) - 21个点
    for i in range(21):
        keypoints.append([1.16, 0.80, 0.5])
    
    return keypoints

def test_coordinate_utils():
    """测试坐标工具类"""
    print("测试坐标工具类功能...")
    
    # 测试距离阈值转换
    pixel_threshold = 50
    normalized_threshold = CoordinateUtils.normalize_distance_threshold(pixel_threshold)
    print(f"像素阈值 {pixel_threshold} -> 归一化阈值 {normalized_threshold:.6f}")
    
    # 测试归一化距离计算
    import numpy as np
    p1 = np.array([0.3, 0.3, 0.8])
    p2 = np.array([0.7, 0.7, 0.8])
    distance = CoordinateUtils.calculate_normalized_distance(p1, p2)
    print(f"点 {p1[:2]} 到 {p2[:2]} 的归一化距离: {distance:.6f}")
    
    # 测试肩宽计算
    keypoints = create_test_keypoints()
    shoulder_width = CoordinateUtils.get_shoulder_width(keypoints)
    print(f"肩宽: {shoulder_width:.6f}")

def test_arm_raise_evaluator():
    """测试手臂举起评估器"""
    print("\n测试手臂举起评估器...")
    
    # 导入必要的类型和类
    from models.data_models import ActionInfo, ActionType, TrainingSide
    from algorithms.evaluators.arm_raise import ArmRaiseEvaluator
    
    # 创建动作信息
    action_info = ActionInfo(
        action_type="arm_raise",
        side="left",
        difficulty_level="medium",
        required_keypoints=[5, 7, 9]  # LEFT_SHOULDER, LEFT_ELBOW, LEFT_WRIST
    )
    
    evaluator = ArmRaiseEvaluator(action_info)
    
    # 设置关键点数据
    keypoints = create_test_keypoints()
    evaluator.kps = keypoints
    
    # 测试识别
    recognized = evaluator.recognize()
    print(f"动作识别结果: {recognized}")
    
    # 测试评分
    score = evaluator.calculate_score()
    print(f"动作得分: {score:.2f}")
    
    # 测试评估结果
    result = evaluator.evaluate(keypoints)
    print(f"评估结果: {result}")

def test_shoulder_touch_evaluator():
    """测试肩膀触摸评估器"""
    print("\n测试肩膀触摸评估器...")
    
    # 创建一个肩膀触摸的姿态
    keypoints = create_test_keypoints()
    # 修改左手位置使其接近右肩膀
    keypoints[9] = [1.06, 0.57, 0.8]  # LEFT_WRIST 移到右肩膀附近
    keypoints[7] = [1.02, 0.53, 0.8]  # LEFT_ELBOW 相应调整
    
    from models.data_models import ActionInfo
    from algorithms.evaluators.shoulder_touch import ShoulderTouchEvaluator
    
    action_info = ActionInfo(
        action_type="shoulder_touch",
        side="left",  # 左手摸右肩膀
        difficulty_level="medium",
        required_keypoints=[5, 6, 7, 9]  # 肩膀、肘部、手腕
    )
    
    evaluator = ShoulderTouchEvaluator(action_info)
    evaluator.kps = keypoints
    
    recognized = evaluator.recognize()
    score = evaluator.calculate_score()
    result = evaluator.evaluate(keypoints)
    
    print(f"肩膀触摸识别: {recognized}, 得分: {score:.2f}")
    print(f"评估结果: {result}")

def main():
    """主函数"""
    print("=== 归一化坐标系统测试 ===")
    
    # 测试坐标工具
    test_coordinate_utils()
    
    # 测试手臂举起评估器
    test_arm_raise_evaluator()
    
    # 测试肩膀触摸评估器
    test_shoulder_touch_evaluator()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
