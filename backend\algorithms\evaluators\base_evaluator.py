# src/algorithms/evaluators/base_evaluator.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Tuple, Optional
from collections import deque
import time
import numpy as np
from models.data_models import ActionInfo
from utils.pose_math import pose_math
from utils.coordinate_utils import CoordinateUtils

class BaseActionEvaluator(ABC):
    """动作评估器的抽象基类 - 专注于实时评分和完成判断"""
    
    def __init__(self, action_info: ActionInfo):
        """初始化评估器"""
        self.action_info = action_info
        self.kps = None
        
        # 时间平滑参数
        self.score_history = deque(maxlen=10)  # 保存最近10帧的得分
        self.stability_window = 5  # 稳定性检测窗口
        self.min_confidence_frames = 3  # 最少连续有效帧数
        
        # 评估阈值 (根据难度调整)
        self.difficulty_multiplier = self._get_difficulty_multiplier()
        self.completion_threshold = 60.0 * self.difficulty_multiplier
        
        # 时间记录
        self.last_evaluation_time = 0.0
        self.continuous_success_count = 0
        
    def evaluate(self, keypoints: List[List[float]]) -> Dict[str, Any]:
        """
        评估一帧的姿态。这是外部调用的主方法。
        返回包含 'score', 'completed' 等核心数据的字典。
        """
        self.kps = keypoints
        current_time = time.time()
    
        # 计算实时得分
        current_score = self.calculate_score()
        
        # 时间平滑处理
        smoothed_score = self._smooth_score(current_score)
        
        # 判断动作是否完成
        completed = self._check_completion(smoothed_score)
        
        # 计算稳定性和置信度
        stability = self._calculate_stability()
        
        self.last_evaluation_time = current_time
        
        return {
            "recognized": current_score > 20.0,  # 基础识别阈值
            "score": smoothed_score,
            "completed": completed,
            "details": {
                "stability": stability,
                "continuous_frames": self.continuous_success_count
            }
        }
    
    
    def _smooth_score(self, current_score: float) -> float:
        """应用时间平滑到得分"""
        self.score_history.append(current_score)
        
        if len(self.score_history) < 3:
            return current_score
        
        # 使用加权平均，最新的分数权重更高
        weights = [0.5, 0.3, 0.2]  # 最新、次新、第三新
        recent_scores = list(self.score_history)[-3:]
        
        weighted_score = sum(score * weight for score, weight in zip(recent_scores, weights))
        return weighted_score
    
    def _check_completion(self, score: float) -> bool:
        """检查动作是否完成"""
        if score >= self.completion_threshold:
            self.continuous_success_count += 1
        else:
            self.continuous_success_count = 0
        
        return self.continuous_success_count >= self.min_confidence_frames
        检查当前帧是否识别到目标动作
        返回: 是否识别到动作
  
        pass
    @abstractmethod
    def calculate_score(self) -> float:
        """
        计算当前帧的动作得分 (0-100)
        """
        pass
    
    def _get_difficulty_multiplier(self) -> float:
        """根据难度调整评分容忍度"""
        level = self.action_info.difficulty_level
        if level == 'easy': 
            return 0.85  # 简单模式降低要求
        elif level == 'medium': 
            return 1.0   # 中等模式正常要求
        elif level == 'hard': 
            return 1.2   # 困难模式提高要求
        return 1.0
    
    def _apply_time_smoothing(self, current_score: float) -> float:
        """应用时间平滑算法"""
        self.score_history.append(current_score)
        
        if len(self.score_history) < 3:
            return current_score
        
        # 使用加权平均，最近的帧权重更高
        weights = np.linspace(0.5, 1.0, len(self.score_history))
        weighted_avg = np.average(list(self.score_history), weights=weights)
        
        return float(weighted_avg)
    
    def _calculate_stability(self) -> float:
        """计算动作稳定性 (0-1)"""
        if len(self.score_history) < 3:
            return 0.0
            
        recent_scores = list(self.score_history)[-self.stability_window:]
        std_dev = np.std(recent_scores)
        
        # 标准差越小，稳定性越高
        stability = max(0.0, 1.0 - (std_dev / 50.0))
        return float(stability)
    
    def _calculate_confidence(self) -> float:
        """计算识别置信度 (0-1)"""
        if len(self.score_history) == 0:
            return 0.0
            
        recent_scores = list(self.score_history)[-3:]
        avg_score = np.mean(recent_scores)
        
        # 得分越高，置信度越高
        confidence = min(avg_score / 100.0, 1.0)
        return float(confidence)