#!/usr/bin/env python3
"""
测试评估器优化 - 验证所有评估器都移除了recognize方法，只保留实时评分功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.data_models import ActionInfo, ActionType, TrainingSide
from algorithms.evaluators_factory import evaluator_factory

def test_evaluator_optimization():
    """测试所有评估器的优化情况"""
    
    print("=== 评估器优化验证测试 ===")
    
    # 测试数据：RTMPose格式的关键点（133个点，每个点[x, y, confidence]）
    # 模拟一个基本的姿态数据
    test_keypoints = []
    for i in range(133):
        x = 1.0 + (i % 10) * 0.1  # RTMPose坐标系：值在1.0左右
        y = 1.0 + (i // 10) * 0.1
        confidence = 0.9
        test_keypoints.append([x, y, confidence])
    
    # 测试所有动作类型
    test_actions = [
        ("arm_raise", "left", "easy", "左手举手"),
        ("arm_raise", "right", "easy", "右手举手"),
        ("shoulder_touch", "left", "easy", "左手摸右肩"),
        ("shoulder_touch", "right", "easy", "右手摸左肩"),
        ("finger_touch", "left", "easy", "双手指尖相触"),  # finger_touch使用left作为默认
        ("palm_flip", "left", "easy", "左手翻掌"),
        ("palm_flip", "right", "easy", "右手翻掌"),
    ]
    
    print("\n1. 检查评估器创建和方法可用性：")
    for action_type, side, difficulty, description in test_actions:
        action_info = ActionInfo(
            action_type=action_type, 
            side=side, 
            difficulty_level=difficulty, 
            required_keypoints=list(range(133))  # 使用所有关键点
        )
        
        try:
            # 创建评估器
            evaluator = evaluator_factory.create_evaluator(action_info)
            print(f"✓ {description}: 评估器创建成功")
            
            # 检查是否还有recognize方法
            if hasattr(evaluator, 'recognize'):
                print(f"  ⚠️  警告: 仍然存在recognize方法")
            else:
                print(f"  ✓ 已移除recognize方法")
            
            # 检查是否有calculate_score方法
            if hasattr(evaluator, 'calculate_score'):
                print(f"  ✓ 存在calculate_score方法")
            else:
                print(f"  ❌ 缺少calculate_score方法")
            
            # 检查evaluate方法
            if hasattr(evaluator, 'evaluate'):
                print(f"  ✓ 存在evaluate方法")
            else:
                print(f"  ❌ 缺少evaluate方法")
                
        except Exception as e:
            print(f"❌ {description}: 评估器创建失败 - {e}")
    
    print("\n2. 测试实时评分功能：")
    for action_type, side, difficulty, description in test_actions:
        action_info = ActionInfo(
            action_type=action_type, 
            side=side, 
            difficulty_level=difficulty, 
            required_keypoints=list(range(133))
        )
        
        try:
            evaluator = evaluator_factory.create_evaluator(action_info)
            
            # 测试evaluate方法
            result = evaluator.evaluate(test_keypoints)
            
            # 验证返回格式
            required_keys = ['score', 'completed']
            if all(key in result for key in required_keys):
                score = result['score']
                completed = result['completed']
                print(f"✓ {description}: 得分={score:.1f}, 完成={completed}")
            else:
                print(f"❌ {description}: 返回格式不正确 - {result}")
                
        except Exception as e:
            print(f"❌ {description}: 评估失败 - {e}")
    
    print("\n3. 测试完成检测逻辑：")
    # 创建一个高分的测试场景
    for action_type, side, difficulty, description in test_actions[:2]:  # 只测试两个动作
        action_info = ActionInfo(
            action_type=action_type, 
            side=side, 
            difficulty_level=difficulty, 
            required_keypoints=list(range(133))
        )
        
        try:
            evaluator = evaluator_factory.create_evaluator(action_info)
            
            # 连续给高分，测试完成检测
            for i in range(5):
                # 第一次低分
                if i == 0:
                    result = evaluator.evaluate(test_keypoints)
                    print(f"  帧{i+1}: 得分={result['score']:.1f}, 完成={result['completed']}")
                else:
                    # 模拟高分场景：修改一些关键点位置来提高得分
                    high_score_keypoints = test_keypoints.copy()
                    # 这里可以根据具体动作调整关键点来获得高分
                    result = evaluator.evaluate(high_score_keypoints)
                    print(f"  帧{i+1}: 得分={result['score']:.1f}, 完成={result['completed']}")
            
        except Exception as e:
            print(f"❌ {description}: 完成检测测试失败 - {e}")

if __name__ == "__main__":
    test_evaluator_optimization()
