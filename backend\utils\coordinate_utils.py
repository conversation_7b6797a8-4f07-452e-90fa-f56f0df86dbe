# utils/coordinate_utils.py
"""
坐标处理工具类 - 处理RTMPose坐标系统
"""
import numpy as np
from typing import List, Optional, Tuple

class CoordinateUtils:
    """RTMPose坐标处理工具类"""
    
    # RTMPose坐标系统的参考尺寸 (基于实际观察到的坐标范围)
    REFERENCE_WIDTH = 1.0   # 参考宽度
    REFERENCE_HEIGHT = 1.0  # 参考高度
    
    @staticmethod
    def normalize_distance_threshold(pixel_threshold: float) -> float:
        """
        将像素距离阈值转换为RTMPose坐标系的阈值
        
        Args:
            pixel_threshold: 像素距离阈值
            
        Returns:
            float: RTMPose坐标系距离阈值
        """
        # 基于640x480分辨率的参考转换
        # RTMPose坐标系中，大致1个单位对应600-800像素
        reference_pixels = 640
        return pixel_threshold / reference_pixels
    
    @staticmethod
    def calculate_normalized_distance(p1: np.ndarray, p2: np.ndarray) -> float:
        """
        计算RTMPose坐标系中两点间距离
        
        Args:
            p1: 点1 [x, y, confidence]
            p2: 点2 [x, y, confidence]
            
        Returns:
            float: 距离
        """
        if p1 is None or p2 is None:
            return float('inf')
        return float(np.linalg.norm(p1[:2] - p2[:2]))
    
    @staticmethod
    def calculate_body_normalized_distance(p1: np.ndarray, p2: np.ndarray, 
                                          shoulder_distance: float) -> float:
        """
        使用肩宽作为参考计算相对距离
        
        Args:
            p1: 点1
            p2: 点2
            shoulder_distance: 肩宽距离
            
        Returns:
            float: 相对于肩宽的距离比例
        """
        if p1 is None or p2 is None or shoulder_distance <= 0:
            return float('inf')
        
        distance = CoordinateUtils.calculate_normalized_distance(p1, p2)
        return distance / shoulder_distance
    
    @staticmethod
    def get_shoulder_width(keypoints: List[List[float]]) -> float:
        """
        获取肩宽(RTMPose坐标系)
        
        Args:
            keypoints: 关键点列表
            
        Returns:
            float: 肩宽距离
        """
        try:
            if len(keypoints) < 7:  # 确保有足够的关键点
                return 0.1  # 默认肩宽
            
            left_shoulder = np.array(keypoints[5])   # LEFT_SHOULDER
            right_shoulder = np.array(keypoints[6])  # RIGHT_SHOULDER
            
            if left_shoulder[2] > 0.3 and right_shoulder[2] > 0.3:
                return CoordinateUtils.calculate_normalized_distance(left_shoulder, right_shoulder)
            else:
                return 0.1  # 默认肩宽
        except:
            return 0.1
    
    @staticmethod
    def get_torso_height(keypoints: List[List[float]]) -> float:
        """
        获取躯干高度(从肩膀到髋部的距离)
        
        Args:
            keypoints: 关键点列表
            
        Returns:
            float: 躯干高度距离
        """
        try:
            if len(keypoints) < 13:
                return 0.3  # 默认躯干高度
            
            # 使用肩膀中点到髋部中点的距离
            left_shoulder = np.array(keypoints[5])
            right_shoulder = np.array(keypoints[6])
            left_hip = np.array(keypoints[11])
            right_hip = np.array(keypoints[12])
            
            # 计算中点
            if (left_shoulder[2] > 0.3 and right_shoulder[2] > 0.3 and 
                left_hip[2] > 0.3 and right_hip[2] > 0.3):
                
                shoulder_center = (left_shoulder[:2] + right_shoulder[:2]) / 2
                hip_center = (left_hip[:2] + right_hip[:2]) / 2
                
                return float(np.linalg.norm(shoulder_center - hip_center))
            else:
                return 0.3  # 默认值
        except:
            return 0.3
    
    @staticmethod
    def get_body_reference_point(keypoints: List[List[float]]) -> Optional[np.ndarray]:
        """
        获取身体参考点(通常是鼻子或肩膀中点)
        
        Args:
            keypoints: 关键点列表
            
        Returns:
            Optional[np.ndarray]: 参考点坐标
        """
        try:
            # 优先使用鼻子
            if len(keypoints) > 0 and keypoints[0][2] > 0.3:
                return np.array(keypoints[0])
            
            # 其次使用肩膀中点
            if len(keypoints) > 6 and keypoints[5][2] > 0.3 and keypoints[6][2] > 0.3:
                left_shoulder = np.array(keypoints[5])
                right_shoulder = np.array(keypoints[6])
                center = (left_shoulder[:2] + right_shoulder[:2]) / 2
                confidence = (left_shoulder[2] + right_shoulder[2]) / 2
                return np.array([center[0], center[1], confidence])
            
            return None
        except:
            return None
    
    @staticmethod
    def relative_height_position(keypoint: np.ndarray, reference_point: np.ndarray) -> float:
        """
        计算关键点相对于参考点的高度位置
        
        Args:
            keypoint: 目标关键点
            reference_point: 参考点
            
        Returns:
            float: 相对高度 (负值表示在参考点上方，正值表示在下方)
        """
        if keypoint is None or reference_point is None:
            return 0.0
        return keypoint[1] - reference_point[1]
    
    @staticmethod
    def is_point_above_reference(keypoint: np.ndarray, reference_point: np.ndarray, 
                                threshold: float = 0.05) -> bool:
        """
        判断点是否在参考点上方
        
        Args:
            keypoint: 目标关键点
            reference_point: 参考点
            threshold: 高度阈值
            
        Returns:
            bool: 是否在上方
        """
        if keypoint is None or reference_point is None:
            return False
        height_diff = CoordinateUtils.relative_height_position(keypoint, reference_point)
        return height_diff < -threshold  # Y坐标向下增加，所以上方是负值
    
    @staticmethod
    def cross_body_midline(left_point: np.ndarray, right_point: np.ndarray, 
                          body_center_x: float) -> bool:
        """
        判断点是否跨越身体中线
        
        Args:
            left_point: 左侧关键点
            right_point: 右侧关键点  
            body_center_x: 身体中线X坐标
            
        Returns:
            bool: 是否跨越中线
        """
        if left_point is None or right_point is None:
            return False
        
        # 左手是否越过中线到右侧
        left_cross = left_point[0] > body_center_x
        # 右手是否越过中线到左侧  
        right_cross = right_point[0] < body_center_x
        
        return left_cross or right_cross
