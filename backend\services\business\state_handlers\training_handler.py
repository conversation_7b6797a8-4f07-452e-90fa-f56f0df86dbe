import time
from typing import Dict, Any, List, Optional

from models.system_states import SystemState, StateTransitionEvent, MessageType
from models.data_models import ZMQDetectData, SystemStateData, CurrentAction, ActionInfo
from algorithms.evaluators_factory import evaluator_factory
from . import BaseStateHandler
from ..user_detection_service import user_detection_service
from ..task.task_loader import task_loader

class TrainingHandler(BaseStateHandler):
    """TRAINING状态处理器 - 使用评估器工厂模式 (优化版)"""
    
    def __init__(self):
        super().__init__(SystemState.TRAINING)
        # 移除旧的阈值设置，现在由评估器内部管理
        self.current_evaluator = None

    def enter_state(self, context: Dict[str, Any]):
        """进入TRAINING状态或切换新动作时调用"""
        self.logger.info("系统进入动作训练状态")
        current_action = context.get("current_action")
        if current_action:
            self.current_evaluator = evaluator_factory.create_evaluator(current_action.action_info)
            # 初始化或重置动作的运行时状态
            current_action.score = 0.0
            current_action.completed = False
            current_action.action_status = "pending"
            current_action.start_time = None
            current_action.end_time = None
            self.logger.info(f"已加载动作评估器: {current_action.action_type}")

    def handle_data(self, data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        user_status = self._check_user_status(data, context)
        if user_status.get("should_pause"):
            return self._handle_pause(user_status, context)

        completed_flag = self._process_pose_data(data.pose_keypoints, context)
        if completed_flag:
            next_action = task_loader.get_next_action()
            if next_action:
                return self._handle_action_switch(context, next_action)
            else:
                return self._handle_all_actions_completed(context)
        
        return self._send_training_message(context)
   
    def _check_user_status(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户状态 - 使用公共用户检测服务"""
        presence_result = user_detection_service.check_user(pose_data, context)
        if presence_result.get("should_pause"):
            return {
                "should_pause": True,
                "pause_reason": presence_result.get("pause_reason"),
                "message": presence_result.get("message")
            }
        return {"should_pause": False}
    
    def _handle_pause(self, user_status: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理暂停逻辑"""
        # ... (此方法保持不变) ...
        pause_reason = user_status["pause_reason"]
        event = StateTransitionEvent.USER_LOST if pause_reason == "user_lost" else StateTransitionEvent.USER_NOT_AUTH
        message_type = MessageType.USER_LOST if pause_reason == "user_lost" else MessageType.USER_NOT_AUTH
        state_data = SystemStateData(current_state=SystemState.PAUSE, message=user_status["message"])
        return {"success": True, "trigger_event": event, "next_state": SystemState.PAUSE, "websocket_message": message_type, "state_data": state_data}
     
    def _handle_action_switch(self, context: Dict[str, Any], next_action_info) -> Dict[str, Any]:
        """处理动作切换，并发送包含完整上下文的状态数据"""
        self.logger.info(f"切换到下一个动作: {next_action_info.action_type}")
         # 更新动作
        context["current_action"] = next_action_info
        # 【优化】构建包含完整上下文的 state_data
        state_data = self._build_full_state_data(SystemState.PREPARATION, context)
        state_data.message = f"动作完成！准备下一个动作: {next_action_info.action_type}"
       
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.ACTION_SWITCH,
            "next_state": SystemState.PREPARATION,
            "websocket_message": MessageType.ACTION_SWITCH,
            "state_data": state_data,
        }
    
    def _handle_all_actions_completed(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理所有动作完成"""
        self.logger.info("所有训练动作均已完成！")
        final_report_data = self._generate_final_report(context)
        
        # 【优化】构建包含完整上下文的 state_data
        state_data = self._build_full_state_data(SystemState.REPORTING, context)
        state_data.message = "恭喜！您已完成所有训练任务。"
        state_data.report_data = final_report_data
        
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.ACTION_COMPLETED,
            "next_state": SystemState.REPORTING,
            "websocket_message": MessageType.ACTION_COMPLETED,
            "state_data": state_data,
        }

    def _send_training_message(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """发送常规训练消息，包含完整上下文"""
        # 【优化】构建包含完整上下文的 state_data
        state_data = self._build_full_state_data(SystemState.TRAINING, context)
        return {
            "success": True,
            "websocket_message": MessageType.TRAINING_MESSAGE,
            "state_data": state_data,
        }

    def _build_full_state_data(self, state: SystemState, context: Dict[str, Any]) -> SystemStateData:
        """【新增】一个辅助方法，用于创建包含所有必要上下文的SystemStateData对象"""
        return SystemStateData(
            current_state=state,
            user_info=context.get("user_info"),
            current_action=context.get("current_action"),
        )
    
    def _process_pose_data(self, keypoints: List[List[float]], context: Dict[str, Any]) -> bool:
        """使用当前评估器处理姿态数据，并更新完成度。"""
        current_action = context["current_action"]
        if not self.current_evaluator:
            self.logger.warning("当前没有可用的动作评估器")
            return False
       
        # 使用评估器评估当前动作
        result = self.current_evaluator.evaluate(keypoints)
        
        # 更新动作状态
        current_action.score = result["score"]
        
        # 根据得分判断识别状态（简化逻辑）
        recognized = result["score"] > 20.0  # 基础识别阈值
        completed = result.get("completed", False)
        
        # 日志记录评估结果
        self.logger.info(f"动作评估结果: 识别={recognized}, 得分={result['score']:.1f}, 完成={completed}")
        
        # 检查动作是否完成
        if completed:
            self.logger.info(f"动作 {current_action.action_type} 完成! 最终得分: {result['score']:.1f}")
            current_action.completed = True
            current_action.end_time = time.time()
            current_action.action_status = "completed"
            return True
        
        # 更新动作状态
        if recognized:
            current_action.action_status = "active"
            if not current_action.start_time:
                current_action.start_time = time.time()
        else:
            current_action.action_status = "pending"
        
        return False

    def _generate_final_report(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终报告"""
        # ... (此方法保持不变) ...
        action_list = context.get("action_list", [])
        total_score = sum(action.score for action in action_list if action.score)
        average_score = total_score / len(action_list) if action_list else 0
        report = {"average_score": round(average_score, 2), "total_actions": len(action_list), "completion_time": time.time() - context.get("session_start_time", time.time()), "action_details": [a.to_dict() for a in action_list]}
        return report

