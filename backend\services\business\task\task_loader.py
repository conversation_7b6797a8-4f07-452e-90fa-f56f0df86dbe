"""
智能康复系统 - 任务加载服务
处理任务加载、验证和管理
"""
import logging
from typing import List, Optional, Dict, Any
from services.business.user.user_manager import user_manager
from models.data_models import ActionInfo, CurrentAction, UserInfo
from models.constants import ActionKeyPoints
import time
class ActionLoader:
    """任务加载器"""
    
    def __init__(self):
        """初始化任务加载器"""
        self.logger = logging.getLogger(__name__)
        self.current_action_index = 0
        self.loaded_actions: List[ActionInfo] = []
        self.current_user: Optional[UserInfo] = None
        
        self.logger.info("任务加载器初始化完成")
    
    def load_user_actions(self, patient_id: str) -> bool:
        """
        加载用户任务
        
        Args:
            patient_id: 患者ID
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 获取用户任务
            actions = user_manager.get_user_actions(patient_id)
            if not actions:
                self.logger.warning(f"用户无分配任务: {patient_id}")
                return False
            self.loaded_actions = actions
            self.current_action_index = 0
            self.logger.info(f"加载用户任务成功: {patient_id} - {len(actions)} 个任务")
            return True
        except Exception as e:
            self.logger.error(f"加载用户任务失败: {e}")
            return False
        
    def get_actions_for_user(self, patient_id: str) -> list:
        """
        加载用户任务
        
        Args:
            patient_id: 患者ID
            
        Returns:
            bool: 加载是否成功
        """
        # 获取用户任务
        actions = user_manager.get_user_actions(patient_id)
        if not actions:
            self.logger.warning(f"用户无分配任务: {patient_id}")
        self.loaded_actions = actions
        self.current_action_index = 0
        return actions
    
    def get_current_action(self) -> Optional[CurrentAction]:
        """
        获取当前任务
        Returns:
            CurrentAction: 当前任务，无任务返回None
        """
        try:
            if not self.loaded_actions or self.current_action_index >= len(self.loaded_actions):
                return None
            current_action_info = self.loaded_actions[self.current_action_index]
            current_action = CurrentAction(
                action_info=current_action_info, 
                action_type=current_action_info.action_type,
                video_url=current_action_info.video_url,
                side=current_action_info.side,
                completed=False,  # 当前组是否完成
                score=0,
            )
            return current_action
            
        except Exception as e:
            self.logger.error(f"获取当前任务失败: {e}")
            return None
    
    def get_next_action(self) -> Optional[CurrentAction]:
        """
        获取下一个任务

        Returns:
            Currentaction: 下一个任务，无更多任务返回None
        """
        try:
            if not self.loaded_actions:
                return None
            self.current_action_index += 1
            if self.current_action_index >= len(self.loaded_actions):
                self.logger.info("所有任务已完成")
                return None
            return self.get_current_action()

        except Exception as e:
            self.logger.error(f"获取下一个任务失败: {e}")
            return None

    def next_action(self) -> bool:
        """
        切换到下一个动作

        Returns:
            bool: 切换成功返回True，无更多任务返回False
        """
        try:
            next_action = self.get_next_action()
            if next_action:
                self.logger.info(f"切换到下一个任务: {next_action.action_info}")
                return True
            else:
                self.logger.info("没有更多任务")
                return False
        except Exception as e:
            self.logger.error(f"任务切换失败: {e}")
            return False

    def has_next_action(self) -> bool:
        """
        检查是否有下一个动作

        Returns:
            bool: 有下一个任务返回True，否则返回False
        """
        try:
            if not self.loaded_actions:
                return False
            return (self.current_action_index + 1) < len(self.loaded_actions)
        except Exception as e:
            self.logger.error(f"检查下一个任务失败: {e}")
            return False
    
    
    def validate_action_completion(self, current_action: CurrentAction) -> bool:
        """
        验证任务是否完成
        
        Args:
            current_action: 当前任务
            
        Returns:
            bool: 任务是否完成
        """
        return True
    
    def reset_actions(self):
        """重置任务状态"""
        try:
            self.current_action_index = 0
            self.loaded_actions = []
            self.current_user = None
            
            self.logger.info("任务状态已重置")
            
        except Exception as e:
            self.logger.error(f"重置任务状态失败: {e}")

    
    def get_all_actions(self) -> List[ActionInfo]:
        """
        获取所有加载的任务
        
        Returns:
            List[actionInfo]: 任务列表
        """
        return self.loaded_actions.copy()

# 全局任务加载器实例
task_loader = ActionLoader()
