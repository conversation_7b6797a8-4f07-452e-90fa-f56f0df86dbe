# src/algorithms/evaluators/arm_raise.py
import numpy as np
from typing import Tuple
from .base_evaluator import BaseActionEvaluator
from models.constants import KeyPointMapping as K
from utils.pose_math import pose_math
from utils.coordinate_utils import CoordinateUtils

class ArmRaiseEvaluator(BaseActionEvaluator):
    """手臂上抬动作评估器 - 专注于实时评分和完成判断"""
    
    def __init__(self, action_info):
        super().__init__(action_info)
        # 动作特定的阈值 - 适配RTMPose坐标系
        self.min_raise_angle = 70  # 最小抬举角度
        self.optimal_raise_angle = 90  # 最佳抬举角度
        self.max_raise_angle = 130  # 最大抬举角度
        self.min_height_diff = 0.08  # 最小高度差（手腕高于肩膀）
        
    def calculate_score(self) -> float:
        """计算手臂抬举实时得分"""
        side = self.action_info.side
        kps = self.kps
        
        if not kps:
            return 0.0
        
        # 获取对应侧的关键点
        shoulder, elbow, wrist = pose_math.get_keypoints(kps, [
            K.LEFT_SHOULDER if side == "left" else K.RIGHT_SHOULDER,
            K.LEFT_ELBOW if side == "left" else K.RIGHT_ELBOW,
            K.LEFT_WRIST if side == "left" else K.RIGHT_WRIST
        ])
        
        if not all(pose_math.is_valid_keypoint(kp) for kp in [shoulder, elbow, wrist]):
            return 0.0
        
        # 计算多个评估指标
        angle_score = self._evaluate_arm_angle(shoulder, elbow, wrist)
        height_score = self._evaluate_arm_height(shoulder, wrist)
        stability_score = self._evaluate_arm_stability(shoulder, elbow, wrist)
        posture_score = self._evaluate_body_posture(shoulder, elbow, wrist)
        
        # 加权综合得分
        total_score = (
            angle_score * 0.4 +      # 角度是最重要的指标
            height_score * 0.35 +    # 高度次之
            stability_score * 0.15 + # 稳定性
            posture_score * 0.1      # 整体姿态
        )
        
        return min(100.0, max(0.0, total_score))
    
    def _evaluate_arm_angle(self, shoulder: np.ndarray, elbow: np.ndarray, wrist: np.ndarray) -> float:
        """评估手臂角度（肩膀-肘部-垂直向上的角度）"""
        # 计算肩膀到肘部的向量
        shoulder_to_elbow = elbow[:2] - shoulder[:2]
        
        # 垂直向上的向量
        vertical_up = np.array([0, -1])  # Y轴负方向是向上
        
        # 计算角度
        angle = self._calculate_vector_angle(shoulder_to_elbow, vertical_up)
        
        # 根据角度给分
        if angle <= self.min_raise_angle:
            return 20.0  # 角度不够
        elif angle <= self.optimal_raise_angle:
            # 线性插值到最高分
            progress = (angle - self.min_raise_angle) / (self.optimal_raise_angle - self.min_raise_angle)
            return 20.0 + progress * 80.0
        elif angle <= self.max_raise_angle:
            # 超过最佳角度后逐渐减分
            over_optimal = angle - self.optimal_raise_angle
            penalty = over_optimal / (self.max_raise_angle - self.optimal_raise_angle) * 20.0
            return 100.0 - penalty
        else:
            return 60.0  # 角度过大
    
    def _evaluate_arm_height(self, shoulder: np.ndarray, wrist: np.ndarray) -> float:
        """评估手臂抬举高度 - 使用RTMPose坐标系"""
        # 计算手腕相对肩膀的高度差 (Y坐标负方向是向上)
        height_diff = shoulder[1] - wrist[1]  # 正值表示手腕在肩膀上方
        
        # 使用身体比例作为参考 - 理想情况下手臂应该抬到头部高度
        # 根据实际RTMPose坐标系观察，合理的抬手高度约为0.15-0.2个单位
        reference_height = 0.15  # RTMPose坐标系中，合理的抬手高度
        
        if height_diff <= 0:
            return 0.0  # 手腕低于或等于肩膀
        elif height_diff >= reference_height:
            return 100.0  # 达到理想高度
        else:
            return (height_diff / reference_height) * 100.0
    
    def _evaluate_arm_stability(self, shoulder: np.ndarray, elbow: np.ndarray, wrist: np.ndarray) -> float:
        """评估手臂稳定性（肘部应该在合理位置）- 使用归一化坐标"""
        # 使用归一化距离计算
        shoulder_wrist_dist = CoordinateUtils.calculate_normalized_distance(shoulder, wrist)
        shoulder_elbow_dist = CoordinateUtils.calculate_normalized_distance(shoulder, elbow)
        elbow_wrist_dist = CoordinateUtils.calculate_normalized_distance(elbow, wrist)
        
        # 理想情况下，肘部应该形成一个合理的弯曲
        expected_total = shoulder_elbow_dist + elbow_wrist_dist
        actual_direct = shoulder_wrist_dist
        
        # 如果手臂完全伸直，比值接近1；如果弯曲合理，比值会大一些
        bend_ratio = expected_total / actual_direct if actual_direct > 0 else 0
        
        if 1.0 <= bend_ratio <= 1.3:
            return 100.0  # 理想弯曲
        elif 1.3 < bend_ratio <= 1.6:
            return 80.0   # 稍微弯曲过度
        else:
            return 60.0   # 弯曲不合理
    
    def _evaluate_body_posture(self, shoulder: np.ndarray, elbow: np.ndarray, wrist: np.ndarray) -> float:
        """评估整体身体姿态"""
        side = self.action_info.side
        
        if not self.kps:
            return 80.0
        
        # 获取另一侧肩膀用于评估身体平衡
        other_shoulder = pose_math.get_keypoints(self.kps, [
            K.RIGHT_SHOULDER if side == "left" else K.LEFT_SHOULDER
        ])[0]
        
        if not pose_math.is_valid_keypoint(other_shoulder):
            return 80.0  # 如果看不到另一侧肩膀，给个中等分
        
        # 检查双肩是否水平（身体是否保持直立）
        shoulder_height_diff = abs(shoulder[1] - other_shoulder[1])
        shoulder_distance = abs(shoulder[0] - other_shoulder[0])
        
        if shoulder_distance > 0:
            tilt_ratio = shoulder_height_diff / shoulder_distance
            if tilt_ratio < 0.1:
                return 100.0  # 身体直立
            elif tilt_ratio < 0.2:
                return 80.0   # 轻微倾斜
            else:
                return 60.0   # 明显倾斜
        
        return 80.0
    
    def _calculate_vector_angle(self, v1: np.ndarray, v2: np.ndarray) -> float:
        """计算两个向量之间的角度（度）"""
        # 归一化向量
        v1_norm = v1 / (np.linalg.norm(v1) + 1e-8)
        v2_norm = v2 / (np.linalg.norm(v2) + 1e-8)
        
        # 计算点积
        dot_product = np.dot(v1_norm, v2_norm)
        
        # 计算角度
        angle_rad = np.arccos(np.clip(dot_product, -1.0, 1.0))
        angle_deg = np.degrees(angle_rad)
        
        return float(angle_deg)
