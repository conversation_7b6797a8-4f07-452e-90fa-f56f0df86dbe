# src/algorithms/evaluators/palm_flip.py
import numpy as np
from typing import Tuple
from collections import deque
from .base_evaluator import BaseActionEvaluator
from models.constants import KeyPointMapping as K
from utils.pose_math import pose_math
from utils.coordinate_utils import CoordinateUtils

class PalmFlipEvaluator(BaseActionEvaluator):
    """手掌翻转动作评估器 - 专注于实时评分和完成判断"""
    
    def __init__(self, action_info):
        super().__init__(action_info)
        # 动作特定的阈值 - 适配RTMPose坐标系
        self.flip_threshold = 0.3  # 翻转检测阈值（角度相关，无需调整）
        self.min_hand_height = 0.08  # 手部最低高度要求
        
        # 翻转状态跟踪
        self.orientation_history = deque(maxlen=10)  # 记录手部朝向历史
        self.flip_count = 0  # 完整翻转次数
        self.last_orientation = None  # 上一次的朝向
        
    def calculate_score(self) -> float:
        """计算手掌翻转动作得分"""
        side = self.action_info.side
        kps = self.kps
        
        if not kps:
            return 0.0
        
        # 获取关键点
        wrist = pose_math.get_keypoints(kps, [
            K.LEFT_WRIST if side == "left" else K.RIGHT_WRIST
        ])[0]
        
        elbow = pose_math.get_keypoints(kps, [
            K.LEFT_ELBOW if side == "left" else K.RIGHT_ELBOW
        ])[0]
        
        shoulder = pose_math.get_keypoints(kps, [
            K.LEFT_SHOULDER if side == "left" else K.RIGHT_SHOULDER
        ])[0]
        
        if not all(pose_math.is_valid_keypoint(kp) for kp in [wrist, elbow, shoulder]):
            return 0.0
        
        # 尝试获取手部关键点（如果可用）
        hand_keypoints = self._get_hand_keypoints(kps, side)
        
        # 计算多个评估指标
        if hand_keypoints:
            orientation_score = self._evaluate_hand_orientation(hand_keypoints)
            flip_score = self._evaluate_flip_motion(hand_keypoints)
        else:
            # 如果没有手部关键点，使用手腕-肘部向量估算
            orientation_score = self._evaluate_arm_orientation(shoulder, elbow, wrist)
            flip_score = self._evaluate_arm_flip_motion(shoulder, elbow, wrist)
        
        height_score = self._evaluate_hand_height(shoulder, wrist)
        stability_score = self._evaluate_motion_stability(shoulder, elbow, wrist)
        
        # 加权综合得分
        total_score = (
            flip_score * 0.4 +        # 翻转动作最重要
            orientation_score * 0.3 + # 朝向变化
            height_score * 0.2 +      # 抬举高度
            stability_score * 0.1     # 动作稳定性
        )
        
        return min(100.0, max(0.0, total_score))
    
    def _get_hand_keypoints(self, kps, side):
        """获取手部关键点（如果可用）"""
        try:
            if side == "left":
                # 获取左手关键点
                hand_start = K.LEFT_HAND_START
                hand_end = K.LEFT_HAND_END
            else:
                # 获取右手关键点  
                hand_start = K.RIGHT_HAND_START
                hand_end = K.RIGHT_HAND_END
            
            if len(kps) > hand_end:
                hand_points = []
                for i in range(hand_start, hand_end + 1):
                    if i < len(kps) and len(kps[i]) >= 3 and kps[i][2] > 0.3:
                        hand_points.append(np.array(kps[i]))
                
                return hand_points if len(hand_points) >= 5 else None
            
        except:
            pass
        
        return None
    
    def _evaluate_hand_orientation(self, hand_keypoints) -> float:
        """评估手部朝向（基于手部关键点）"""
        if not hand_keypoints or len(hand_keypoints) < 5:
            return 50.0
        
        # 计算手部主轴方向
        # 这里简化为手腕到中指尖的向量
        try:
            wrist_point = hand_keypoints[0]  # 手腕
            fingertip_point = hand_keypoints[len(hand_keypoints)//2]  # 大致中指位置
            
            orientation_vector = fingertip_point[:2] - wrist_point[:2]
            orientation_angle = np.arctan2(orientation_vector[1], orientation_vector[0])
            
            # 记录朝向历史
            self.orientation_history.append(orientation_angle)
            
            return 75.0  # 基础分数，如果能检测到手部关键点
            
        except:
            return 50.0
    
    def _evaluate_arm_orientation(self, shoulder: np.ndarray, elbow: np.ndarray, wrist: np.ndarray) -> float:
        """基于手臂关键点评估朝向"""
        # 计算前臂向量（肘部到手腕）
        forearm_vector = wrist[:2] - elbow[:2]
        orientation_angle = np.arctan2(forearm_vector[1], forearm_vector[0])
        
        # 记录朝向历史
        self.orientation_history.append(orientation_angle)
        
        return 60.0  # 基础朝向分数
    
    def _evaluate_flip_motion(self, hand_keypoints) -> float:
        """评估翻转动作（基于手部关键点变化）"""
        if len(self.orientation_history) < 5:
            return 30.0
        
        # 分析朝向变化
        orientations = list(self.orientation_history)
        angle_changes = []
        
        for i in range(1, len(orientations)):
            angle_diff = abs(orientations[i] - orientations[i-1])
            # 处理角度跨越问题（-π到π的跳跃）
            if angle_diff > np.pi:
                angle_diff = 2 * np.pi - angle_diff
            angle_changes.append(angle_diff)
        
        # 计算总变化量
        total_change = sum(angle_changes)
        
        # 翻转评分：变化量越大，分数越高
        if total_change >= np.pi:  # 至少180度变化
            return 100.0
        elif total_change >= np.pi * 0.75:  # 135度变化
            return 85.0
        elif total_change >= np.pi * 0.5:   # 90度变化
            return 70.0
        elif total_change >= np.pi * 0.25:  # 45度变化
            return 50.0
        else:
            return 30.0
    
    def _evaluate_arm_flip_motion(self, shoulder: np.ndarray, elbow: np.ndarray, wrist: np.ndarray) -> float:
        """基于手臂关键点评估翻转动作"""
        return self._evaluate_flip_motion(None)  # 复用相同逻辑
    
    def _evaluate_hand_height(self, shoulder: np.ndarray, wrist: np.ndarray) -> float:
        """评估手部抬举高度"""
        height_diff = shoulder[1] - wrist[1]  # 手腕相对肩膀的高度
        
        if height_diff >= self.min_hand_height * 2:
            return 100.0  # 抬举很高
        elif height_diff >= self.min_hand_height:
            return 80.0   # 抬举足够
        elif height_diff >= self.min_hand_height * 0.5:
            return 60.0   # 抬举一般
        elif height_diff >= 0:
            return 40.0   # 轻微抬举
        else:
            return 20.0   # 手低于肩膀
    
    def _evaluate_motion_stability(self, shoulder: np.ndarray, elbow: np.ndarray, wrist: np.ndarray) -> float:
        """评估动作稳定性"""
        # 检查手臂是否过度摆动
        arm_length = pose_math.calculate_distance(shoulder, wrist)
        elbow_shoulder_dist = pose_math.calculate_distance(shoulder, elbow)
        elbow_wrist_dist = pose_math.calculate_distance(elbow, wrist)
        
        # 理想情况下，肘部应该在合理位置
        expected_total = elbow_shoulder_dist + elbow_wrist_dist
        bend_ratio = expected_total / arm_length if arm_length > 0 else 0
        
        if 1.1 <= bend_ratio <= 1.4:
            return 100.0  # 理想弯曲
        elif 1.0 <= bend_ratio < 1.1 or 1.4 < bend_ratio <= 1.6:
            return 80.0   # 可接受弯曲
        else:
            return 60.0   # 弯曲异常

