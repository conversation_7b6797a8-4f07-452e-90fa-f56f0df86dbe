# src/algorithms/evaluators/shoulder_touch.py
import numpy as np
from typing import Tuple
from .base_evaluator import BaseActionEvaluator
from models.constants import KeyPointMapping as K
from utils.pose_math import pose_math
from utils.coordinate_utils import CoordinateUtils

class ShoulderTouchEvaluator(BaseActionEvaluator):
    """肩膀触摸动作评估器 - 专注于实时评分和完成判断"""
    
    def __init__(self, action_info):
        super().__init__(action_info)
        # 动作特定的阈值 - 适配RTMPose坐标系
        self.touch_distance_threshold = 0.08  # 手腕到肩膀的距离阈值
        self.optimal_distance = 0.05  # 最佳接触距离
        
    def calculate_score(self) -> float:
        """计算肩膀触摸动作得分"""
        side = self.action_info.side
        kps = self.kps
        
        if not kps:
            return 0.0
        
        # 获取关键点
        if side == "left":
            # 左手摸右肩膀
            wrist = pose_math.get_keypoints(kps, [K.LEFT_WRIST])[0]
            elbow = pose_math.get_keypoints(kps, [K.LEFT_ELBOW])[0]
            shoulder = pose_math.get_keypoints(kps, [K.LEFT_SHOULDER])[0]
            target_shoulder = pose_math.get_keypoints(kps, [K.RIGHT_SHOULDER])[0]
        else:
            # 右手摸左肩膀
            wrist = pose_math.get_keypoints(kps, [K.RIGHT_WRIST])[0]
            elbow = pose_math.get_keypoints(kps, [K.RIGHT_ELBOW])[0]
            shoulder = pose_math.get_keypoints(kps, [K.RIGHT_SHOULDER])[0]
            target_shoulder = pose_math.get_keypoints(kps, [K.LEFT_SHOULDER])[0]
        
        if not all(pose_math.is_valid_keypoint(kp) for kp in [wrist, elbow, shoulder, target_shoulder]):
            return 0.0
        
        # 计算多个评估指标
        distance_score = self._evaluate_touch_distance(wrist, target_shoulder)
        trajectory_score = self._evaluate_arm_trajectory(shoulder, elbow, wrist, target_shoulder)
        posture_score = self._evaluate_body_posture(shoulder, target_shoulder)
        precision_score = self._evaluate_touch_precision(wrist, target_shoulder)
        
        # 加权综合得分
        total_score = (
            distance_score * 0.4 +     # 接触距离最重要
            trajectory_score * 0.3 +   # 手臂轨迹
            precision_score * 0.2 +    # 接触精度
            posture_score * 0.1        # 整体姿态
        )
        
        return min(100.0, max(0.0, total_score))
    
    def _evaluate_touch_distance(self, wrist: np.ndarray, target_shoulder: np.ndarray) -> float:
        """评估接触距离 - 使用RTMPose坐标系"""
        distance = CoordinateUtils.calculate_normalized_distance(wrist, target_shoulder)
        
        if distance <= self.optimal_distance:
            return 100.0  # 完美接触
        elif distance <= self.touch_distance_threshold:
            # 线性衰减 - 调整衰减率适应RTMPose坐标系
            score = 100.0 - (distance - self.optimal_distance) * 500  # 调整衰减率
            return max(60.0, score)
        else:
            return 0.0  # 距离太远
    
    def _evaluate_arm_trajectory(self, shoulder: np.ndarray, elbow: np.ndarray, 
                                wrist: np.ndarray, target_shoulder: np.ndarray) -> float:
        """评估手臂运动轨迹"""
        # 检查手臂是否跨过身体中线
        body_center_x = (shoulder[0] + target_shoulder[0]) / 2
        
        # 手腕应该跨过身体中线
        if self.action_info.side == "left":
            crosses_midline = wrist[0] > body_center_x
        else:
            crosses_midline = wrist[0] < body_center_x
        
        if not crosses_midline:
            return 40.0  # 没有跨过中线
        
        # 评估手臂弯曲角度
        arm_angle = pose_math.calculate_angle(shoulder, elbow, wrist)
        
        # 理想角度范围：90-120度
        if 90 <= arm_angle <= 120:
            return 100.0
        elif 70 <= arm_angle < 90 or 120 < arm_angle <= 140:
            return 80.0
        else:
            return 60.0
    
    def _evaluate_body_posture(self, shoulder: np.ndarray, target_shoulder: np.ndarray) -> float:
        """评估身体姿态"""
        # 检查双肩是否保持水平
        shoulder_height_diff = abs(shoulder[1] - target_shoulder[1])
        shoulder_distance = abs(shoulder[0] - target_shoulder[0])
        
        if shoulder_distance > 0:
            tilt_ratio = shoulder_height_diff / shoulder_distance
            if tilt_ratio < 0.1:
                return 100.0  # 身体直立
            elif tilt_ratio < 0.2:
                return 80.0   # 轻微倾斜
            else:
                return 60.0   # 明显倾斜
        
        return 80.0
    
    def _evaluate_touch_precision(self, wrist: np.ndarray, target_shoulder: np.ndarray) -> float:
        """评估接触精度（手腕位置相对于肩膀的准确性）"""
        distance = pose_math.calculate_distance(wrist, target_shoulder)
        
        # 水平偏差
        horizontal_offset = abs(wrist[0] - target_shoulder[0])
        
        # 垂直偏差  
        vertical_offset = abs(wrist[1] - target_shoulder[1])
        
        # 综合偏差评估
        total_offset = np.sqrt(horizontal_offset**2 + vertical_offset**2)
        
        if total_offset <= 20:
            return 100.0  # 非常精确
        elif total_offset <= 40:
            return 80.0   # 比较精确
        elif total_offset <= 60:
            return 60.0   # 一般精确
        else:
            return 40.0   # 不够精确